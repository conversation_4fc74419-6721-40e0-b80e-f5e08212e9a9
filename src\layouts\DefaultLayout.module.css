/* CSS 变量定义 */
:root {
  --content-animation-duration: 0.4s;
  --content-animation-timing: ease-out;
  --content-slide-distance: -50px;
}

/* 布局样式 */
.layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
.sider {
  background: #fff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  border-right: 1px solid #f0f0f0;
  transition: all 0.2s;
}

.sider.collapsed {
  width: 80px !important;
}

/* Logo 区域 */
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.logo.collapsed {
  justify-content: center;
  padding: 0;
}

.logo.expanded {
  justify-content: flex-start;
  padding: 0 24px;
}

.logoText {
  font-weight: bold;
  color: #1890ff;
  transition: all 0.3s;
}

.logoText.collapsed {
  font-size: 20px;
}

.logoText.expanded {
  font-size: 18px;
}

/* 头部样式 */
.header {
  padding: 0 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

/* 折叠按钮 */
.triggerButton {
  font-size: 16px;
  width: 40px;
  height: 40px;
  transition: all 0.3s;
}

.triggerButton:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

/* 用户信息区域 */
.userArea {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notificationButton {
  font-size: 16px;
  width: 40px;
  height: 40px;
  transition: all 0.3s;
}

.notificationButton:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.userDropdown {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.userDropdown:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.userName {
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

/* 内容区域 */
.content {
  margin: 24px;
  padding: 24px;
  min-height: 280px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
              0 1px 6px -1px rgba(0, 0, 0, 0.02),
              0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* 内容包装器 - 页面切换动画 */
.contentWrapper {
  /* 可选的动画效果：
     fadeSlideIn - 标准fade-slide效果
     fadeSlideInFast - 快速fade-slide效果
     fadeSlideInBlur - 带模糊的fade-slide效果
     fadeSlideInScale - 带缩放的fade-slide效果
  */
  animation: fadeSlideIn 0.4s ease-out;
  transform: translateX(0);
  opacity: 1;
  will-change: transform, opacity;
}

/* Fade-Slide 组合动画 */
@keyframes fadeSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  50% {
    opacity: 0.6;
    transform: translateX(-10px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade-Slide 快速版本 */
@keyframes fadeSlideInFast {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade-Slide 带模糊效果 */
@keyframes fadeSlideInBlur {
  0% {
    opacity: 0;
    transform: translateX(-25px);
    filter: blur(3px);
  }
  70% {
    opacity: 0.8;
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0);
  }
}

/* Fade-Slide 带缩放效果 */
@keyframes fadeSlideInScale {
  0% {
    opacity: 0;
    transform: translateX(-25px) scale(0.96);
  }
  60% {
    opacity: 0.7;
    transform: translateX(-5px) scale(0.99);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 菜单样式 */
.menu {
  border: none;
  font-size: 14px;
}

.menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

.menu .ant-menu-submenu {
  margin: 4px 8px;
}

.menu .ant-menu-submenu > .ant-menu-submenu-title {
  border-radius: 6px;
  height: 40px;
  line-height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sider {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
  }
  
  .header {
    padding: 0 16px;
  }
  
  .content {
    margin: 16px;
    padding: 16px;
  }
}

/* 动画效果 */
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标签页样式 */
.tabsContainer {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  height: 30px;
  padding: 0 6px;
  display: flex;
  align-items: center;
}

.customTabs {
  height: 30px !important;
  min-height: 30px !important;
}

.customTabs .ant-tabs-nav {
  margin: 0 !important;
  height: 30px !important;
}

.customTabs .ant-tabs-tab {
  height: 28px !important;
  line-height: 28px !important;
  padding: 0 12px !important;
  margin: 0 2px !important;
  font-size: 12px !important;
}

.customTabs .ant-tabs-tab-btn {
  height: 28px !important;
  line-height: 28px !important;
}

.customTabs .ant-tabs-tab-remove {
  margin-left: 4px !important;
  font-size: 10px !important;
}

.customTabs .ant-tabs-nav-wrap {
  height: 30px !important;
}

.customTabs .ant-tabs-nav-list {
  height: 30px !important;
}

/* 滚动条样式 */
.sider::-webkit-scrollbar {
  width: 6px;
}

.sider::-webkit-scrollbar-track {
  background: transparent;
}

.sider::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.sider::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
